package com.chidhagni.donationreceipt.donorgroups;

import com.chidhagni.donationreceipt.donorgroups.dto.response.GetEmailTemplatesDto;
import com.chidhagni.donationreceipt.donorgroups.utils.DonorGroupsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.HelperClassToSendCommunication;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class DonorGroupsServiceEmailTemplatesUTest {

    @Mock
    private DonorGroupsRepository donorGroupsRepository;
    
    @Mock
    private DonorGroupsMapper donorGroupMapper;
    
    @Mock
    private DonorsRepository donorsRepository;
    
    @Mock
    private NotificationManager notificationManager;
    
    @Mock
    private OrganizationRepository organizationRepository;
    
    @Mock
    private WatiService watiService;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private HelperClassToSendCommunication helperClassToSendCommunication;

    private DonorGroupsService donorGroupsService;

    @BeforeEach
    void setUp() {
        donorGroupsService = new DonorGroupsService(
                donorGroupsRepository,
                donorGroupMapper,
                donorsRepository,
                notificationManager,
                organizationRepository,
                watiService,
                objectMapper,
                helperClassToSendCommunication
        );
    }

    @Test
    void testGetEmailTemplates_ShouldReturnListOfTemplates() {
        // When
        List<GetEmailTemplatesDto> result = donorGroupsService.getEmailTemplates();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // Verify that we have the expected templates
        Set<String> templateNames = result.stream()
                .map(GetEmailTemplatesDto::getTemplateName)
                .collect(Collectors.toSet());
        
        // Check for some known templates
        assertTrue(templateNames.contains("campaign-announcement"));
        assertTrue(templateNames.contains("campaign-thank-you-note"));
        assertTrue(templateNames.contains("receipt-email"));
        assertTrue(templateNames.contains("email-verification"));
        
        // Verify each DTO has both id and templateName
        for (GetEmailTemplatesDto template : result) {
            assertNotNull(template.getId());
            assertNotNull(template.getTemplateName());
            assertFalse(template.getTemplateName().isEmpty());
        }
        
        // Verify all IDs are unique
        Set<String> uniqueIds = result.stream()
                .map(dto -> dto.getId().toString())
                .collect(Collectors.toSet());
        assertEquals(result.size(), uniqueIds.size(), "All template IDs should be unique");
    }

    @Test
    void testGetEmailTemplates_ShouldGenerateRandomUUIDs() {
        // When - call the method twice
        List<GetEmailTemplatesDto> result1 = donorGroupsService.getEmailTemplates();
        List<GetEmailTemplatesDto> result2 = donorGroupsService.getEmailTemplates();

        // Then - the UUIDs should be different between calls
        Set<String> ids1 = result1.stream()
                .map(dto -> dto.getId().toString())
                .collect(Collectors.toSet());
        
        Set<String> ids2 = result2.stream()
                .map(dto -> dto.getId().toString())
                .collect(Collectors.toSet());
        
        // The IDs should be different (since they're randomly generated)
        assertNotEquals(ids1, ids2, "UUIDs should be randomly generated and different between calls");
    }
}
