package com.chidhagni.donationreceipt.donorgroups;

import com.chidhagni.donationreceipt.donorgroups.dto.response.GetEmailTemplatesDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(DonorGroupsController.class)
class DonorGroupsControllerEmailTemplatesUTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DonorGroupsService donorGroupsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testGetEmailTemplates_ShouldReturnListOfTemplates() throws Exception {
        // Given
        List<GetEmailTemplatesDto> mockTemplates = Arrays.asList(
                GetEmailTemplatesDto.builder()
                        .id(UUID.randomUUID())
                        .templateName("campaign-announcement")
                        .build(),
                GetEmailTemplatesDto.builder()
                        .id(UUID.randomUUID())
                        .templateName("receipt-email")
                        .build(),
                GetEmailTemplatesDto.builder()
                        .id(UUID.randomUUID())
                        .templateName("email-verification")
                        .build()
        );

        when(donorGroupsService.getEmailTemplates()).thenReturn(mockTemplates);

        // When & Then
        mockMvc.perform(get("/api/v1/donor-groups/email-templates")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[0].id").exists())
                .andExpect(jsonPath("$[0].templateName").value("campaign-announcement"))
                .andExpect(jsonPath("$[1].templateName").value("receipt-email"))
                .andExpect(jsonPath("$[2].templateName").value("email-verification"));
    }

    @Test
    void testGetEmailTemplates_ShouldReturnEmptyListWhenNoTemplates() throws Exception {
        // Given
        when(donorGroupsService.getEmailTemplates()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/v1/donor-groups/email-templates")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }
}
