#Web Related Configurations
server.servlet.context-path=/donationreceipt/
server.hostUrl=http://localhost:3000

//CORS configuration
cors.allowed-origins=http://localhost:3000/,http://localhost:3001/
cors.allowed-methods=GET,POST,PUT,DELETE,PATCH,OPTIONS
cors.allow-credentials=true
cors.max-age=3600


# Springdoc OpenAPI Configurations
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
springdoc.packages-to-scan=com.chidhagni

# DB Configurations
spring.datasource.url=*************************************************
spring.datasource.username=donationreceipt
spring.datasource.password=donationreceipt
spring.datasource.driver-class-name=org.postgresql.Driver


# Logging level for various modules
logging.level.root=info
#logging.level.org.springframework.security=debug
#logging.level.org.jooq = debug
email.host=smtp.gmail.com
email.port=587
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

email.contactus-send.to=<EMAIL>
email-readiness-send.to=<EMAIL>
user-registration-send.to=<EMAIL>
jwt.secret=verylongchidhganilocalsecretyouwillnotunderstanddahsdsjalkdjsadnsadsalkdjsalkdhacydsahfdlkfjdslkfjdsfpodsifpdsycxiovlkcxvfdfjdshfiusyfuyrerewjrewfdsadsadsadsadsadasdsadsadsadcxvcxvdf


#8 hour in millis
accessToken.expiry=28800000
#24 hours in millis
refreshToken.expiry=86400000


#Professional Credentials
email.professionals.password=fqactehafmzlltzz
email.professionals=<EMAIL>

#Registration Credentials
email.registration=<EMAIL>
email.registration.password=kabvmidquyyzdrdk

#Societies Credentials
email.societies=<EMAIL>
email.societies.password=hezvdleznamimgzj

#Developers Credentials
email.developers=<EMAIL>
email.developers.password=caeeznabqonwmkxg



#Donation Credentials
email.donations.password=fqactehafmzlltzz
email.donations=<EMAIL>

logging.file.path=/var/log/houzer_logs
logging.file.name=houzer_logs.log
logging.logback.rollingpolicy.max-file-size=5MB
logging.logback.rollingpolicy.max-history=5+

email.submit.enquiry.services.cc=<EMAIL>

spring.security.oauth2.client.registration.google.clientId=1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=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
spring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}
spring.security.oauth2.client.registration.google.scope=email, profile
app.oauth2.authorizedRedirectUris=http://localhost:3000/donationreceipt/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect


##############DMS with Minio#############
filestore.bucket.name=houzer
filestore.access.name=minio
filestore.access.secret=minio123
filestore.url=http://127.0.0.1:9000

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

task.status.listname.id=c166e081-9b82-49ec-9a1a-0428e426e33b
conversation.lead-status.closed.list-value.id=123e4567-e89b-12d3-a456-426655440000
services.listname.id=88cf3b1d-8548-41d3-9910-c2f92dcf2815

# SUPER_ADMIN databaseInitializer
admin.name=Super Admin
admin.id=107dc277-e80a-4d4c-9f0a-48bcbad5bea4
admin.email=<EMAIL>
admin.phone=9999999999
adminPassword=Admin@123
admin.role.type.id=7e63a16c-04da-4e8b-a13e-7e43e5c6d8d3
admin.role.name=SuperAdmin

#wati credentials
token=Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ROkzllJbLB3NiqaVReK4zqVWjfmoksUMakOXRF2SSoU
apiUrl=https://live-mt-server.wati.io/321777

# Wati api Urls
templateMessageUrl=https://live-mt-server.wati.io/321777/api/v1/sendTemplateMessage?whatsappNumber=
sessionFileUrl=https://live-mt-server.wati.io/321777/api/v1/sendSessionFile/
templateMessagesUrl=https://live-mt-server.wati.io/321777/api/v1/sendTemplateMessages
sessionMessagesUrl=https://live-mt-server.wati.io/321777/api/v1/sendTemplateMessages/v1/sendTemplateMessages/

#RazorPay key and secret
rzp-client-Id=rzp_test_WydCsRrfzPdpt3
rzp-client-Secret=PletPOGTatjcDHxrWjJSxOOB


#Chidhagni Employees Company Name
chidhagni.employee.company.name=CHIDHAGNI


roles.tenant.admin=c11e1f0a-cd55-41e6-92ec-8e7c41f72d67
donation.receipt.category=aa053b8f-29fd-4120-8fc1-4d8c9a757e70
donation.receipt.subcategory.8g=a5fa1861-accf-45b6-86ed-fae73991bd93
donation.receipt.subcategory.logo=8fbbe939-046e-4dab-90ee-613ae09f36cf
donation.receipt.recipient.org=5a4d79f1-9c67-4b4f-92b5-fbc6b0cc94f8
donation.receipt.recipient.individual=abc72613-a04c-4690-a28f-98f0efa7ab10

roles.tenant=f5c03c6e-3e4f-45f2-b06f-207a59f0e312
roles.finance.account=ccac9452-763f-4d62-81b3-8b4584de3b38
roles.data.entry-operator=de94d105-ea2b-4777-a4a9-b84b77c03ea1
roles.donor=6f1c3911-94a7-4c86-9444-8f1a5ac72bfc
donor.type.entity=a4a48b76-f25e-44bc-a81d-d5634f5b5b86

roles.superadmin=f4a9c1b2-8e74-4cd7-9a67-3c1f1e9e8f24

msg91.auth-key=446638AKLU50SZ67fcd7faP1
msg91.template-id=68185808d6fc05336870b012
msg91.sender-id=CHIDGN
send.otp.url: https://control.msg91.com/api/v5/otp
verify.otp.url: https://control.msg91.com/api/v5/otp/verify?mobile=%s&otp=%s
resend.otp.url: https://control.msg91.com/api/v5/otp/retry?mobile=%s&retrytype=%s


donation.receipt.pdf=donation_receipt_pdf_dynamic
listnames.id.states=f17a2e1c-c287-4380-8bcb-54d5f23f6cd0


aes.encryption.key=dGhpc2lzYXNlY3JldGtleTEyMzQ1Njc4OTAxMjM0NTY=