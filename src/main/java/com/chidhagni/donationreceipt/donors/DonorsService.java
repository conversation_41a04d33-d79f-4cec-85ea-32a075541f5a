package com.chidhagni.donationreceipt.donors;


import com.chidhagni.donationreceipt.common.exception.DuplicateDonorException;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonorDropdownResponse;
import com.chidhagni.donationreceipt.donorgroups.constants.CommunicationModeEnums;
import com.chidhagni.donationreceipt.donorgroups.dto.request.CommunicationRequestDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorEmailContextDTO;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsDTO;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsPaginationRequest;
import com.chidhagni.donationreceipt.donors.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.donors.dto.response.GetAllDonors;
import com.chidhagni.donationreceipt.donors.utils.DonorMapper;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.wati.WatiDTO;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.HelperClassToSendCommunication;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.tables.Donors.DONORS;

@Service
@RequiredArgsConstructor
@Slf4j
public class DonorsService {

    private final DonorsRepository donorsRepository;
    private final DonorMapper donorMapper;
    private static final Pattern PAN_NO_PATTERN = Pattern.compile(
            "^[A-Z]{5}[0-9]{4}[A-Z]$|^$");
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;
    private final OrganizationRepository organizationRepository;
    private final NotificationManager notificationManager;
    private final WatiService watiService;
    private final ObjectMapper objectMapper;
    private final HelperClassToSendCommunication helperClassToSendCommunication;


    public UUID create(DonorsDTO donorsDTO, UserPrincipal userPrincipal) {
        validateDonors(donorsDTO);
        if (donorsRepository.existsByTenantOrgIdAndEmailAndPanNo(
                donorsDTO.getTenantOrgId(),
                donorsDTO.getEmail(),
                donorsDTO.getPanNo())) {
            throw new DuplicateDonorException(
                    "Donor with the same tenant, email, and PAN number already exists");
        }
        Donors donors = donorMapper.mapToDonors(donorsDTO, userPrincipal);
        Donors createdDonor = donorsRepository.create(donors);
        return createdDonor.getId();
    }


    public DonorsResponse getById(UUID id) {
        Donors donors = donorsRepository.getByDonorId(id);
        if (donors == null) {
            throw new IllegalArgumentException("Donor Not found");
        }
        return donorMapper.mapToDonorsResponse(donors);
    }

    public DonorsResponse update(UUID id, DonorsDTO donorsDto, UserPrincipal userPrincipal) {
        Donors existingDonor = donorsRepository.getByDonorId(id);
        if (existingDonor == null) {
            throw new IllegalArgumentException("Donor Not found");
        }
        UUID tenantOrgId = donorsDto.getTenantOrgId() != null ? donorsDto.getTenantOrgId() : existingDonor.getTenantOrgId();
        String email = donorsDto.getEmail() != null ? donorsDto.getEmail() : existingDonor.getEmail();
        String panNo = donorsDto.getPanNo() != null ? donorsDto.getPanNo() : existingDonor.getPanNo();

        if ((donorsDto.getTenantOrgId() != null || donorsDto.getEmail() != null || donorsDto.getPanNo() != null)
                && donorsRepository.existsByTenantOrgIdAndEmailAndPanNoExcludingId(id, tenantOrgId, email, panNo)) {
            throw new DuplicateDonorException(
                    "Donor with the same tenant organization ID, email, and PAN number already exists");
        }

        if (donorsDto.getName() != null) {
            existingDonor.setName(donorsDto.getName());
        }
        if (donorsDto.getEmail() != null) {
            existingDonor.setEmail(donorsDto.getEmail());
        }
        if (donorsDto.getContactNumber() != null) {
            existingDonor.setMobileNumber(donorsDto.getContactNumber());
        }
        if (donorsDto.getTenantOrgId() != null) {
            existingDonor.setTenantOrgId(donorsDto.getTenantOrgId());
        }
        if (donorsDto.getPanNo() != null) {
            existingDonor.setPanNo(donorsDto.getPanNo());
        }
        if (donorsDto.getIsActive() != null) {
            existingDonor.setIsActive(donorsDto.getIsActive());
        }
        if (donorsDto.getDonorMetaData() != null) {
            existingDonor.setMetaData(donorsDto.getDonorMetaData());
        }
        existingDonor.setUpdatedBy(userPrincipal.getId());
        existingDonor.setUpdatedOn(DateUtils.currentTimeIST());
        donorsRepository.update(existingDonor);
        return donorMapper.mapToDonorsResponse(existingDonor);
    }

    private void validateDonors(DonorsDTO donors) {
        if (donors.getEmail() == null || donors.getEmail().isBlank()) {
            throw new IllegalArgumentException("Email is required");
        }
        if (donors.getTenantOrgId() == null) {
            throw new IllegalArgumentException("Tenant organization ID is required");
        }
        if (donors.getPanNo() != null && !donors.getPanNo().isEmpty()
                && !PAN_NO_PATTERN.matcher(donors.getPanNo()).matches()) {
            throw new IllegalArgumentException("PAN number must have 10 characters: " +
                    "first 5 uppercase letters, followed by 4 digits, and ending with 1 uppercase letter. " +
                    "Example: **********");

        }
    }

    public GetAllDonors getAllDonor(DonorsPaginationRequest donorsPaginationRequest, UserPrincipal userPrincipal) {
        applyDefaults(donorsPaginationRequest);
        Condition finalCondition = getSearchingCondition(donorsPaginationRequest);
        List<SortField<?>> sortFields = new ArrayList<>();
        List<DonorsResponse> allDonorsResponses = donorsRepository.getAllDonors(finalCondition, sortFields,
                donorsPaginationRequest, userPrincipal);

        Integer count = donorsRepository.getDonorCount(finalCondition, userPrincipal);
        return GetAllDonors.builder()
                .donorsResponseList(allDonorsResponses)
                .rowCount(count)
                .build();

    }

    private Condition getSearchingCondition(DonorsPaginationRequest donorsPaginationRequest) {
        String searchByName = donorsPaginationRequest.getNameFilter();
        String searchByEmail = donorsPaginationRequest.getEmailFilter();
        String searchByMobile = donorsPaginationRequest.getMobileFilter();
        String searchByPanNo = donorsPaginationRequest.getPanNoFilter();
        UUID orgId = donorsPaginationRequest.getOrgId();
        String searchByAddress = donorsPaginationRequest.getAddressFilter();
        String searchByState = donorsPaginationRequest.getStateFilter();
        String searchByPinCode = donorsPaginationRequest.getPinCodeFilter();
        List<UUID> searchByTags = donorsPaginationRequest.getTagsFilter();

        List<Condition> conditions = new ArrayList<>();

        if (searchByName != null && !searchByName.isEmpty()) {
            conditions.add(DONORS.NAME.containsIgnoreCase(searchByName));
        }

        if (searchByEmail != null && !searchByEmail.isEmpty()) {
            conditions.add(DONORS.EMAIL.containsIgnoreCase(searchByEmail));
        }

        if (searchByMobile != null && !searchByMobile.isEmpty()) {
            conditions.add(DONORS.MOBILE_NUMBER.containsIgnoreCase(searchByMobile));
        }

        if (searchByPanNo != null && !searchByPanNo.isEmpty()) {
            conditions.add(DONORS.PAN_NO.containsIgnoreCase(searchByPanNo));
        }

        if (orgId != null) {
            conditions.add(DONORS.TENANT_ORG_ID.eq(orgId));
        }

        if (searchByAddress != null && !searchByAddress.isEmpty()) {
            conditions.add(DSL.field("DONORS.meta_data->>'address'", String.class)
                    .containsIgnoreCase(searchByAddress));
        }

        if (searchByState != null && !searchByState.isEmpty()) {
            conditions.add(DSL.field("DONORS.meta_data->>'state'", String.class)
                    .containsIgnoreCase(searchByState));
        }

        if (searchByPinCode != null && !searchByPinCode.isEmpty()) {
            conditions.add(DSL.field("DONORS.meta_data->>'pinCode'", String.class)
                    .containsIgnoreCase(searchByPinCode));
        }

        if (searchByTags != null && !searchByTags.isEmpty()) {
            List<String> tagStrings = searchByTags.stream()
                    .filter(Objects::nonNull)
                    .map(UUID::toString)
                    .collect(Collectors.toList());

            if (!tagStrings.isEmpty()) {
                conditions.add(DSL.condition(
                        "meta_data->'tags' ??| array[" +
                        tagStrings.stream().map(tag -> "?").collect(Collectors.joining(",")) +
                        "]",
                        tagStrings.toArray()
                ));
            }
        }

        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
    }

    public void applyDefaults(DonorsPaginationRequest donorsPaginationRequest) {
        if (donorsPaginationRequest.getPageSize() == null || donorsPaginationRequest.getPageSize() < 1) {
            donorsPaginationRequest.setPageSize(pageSize);
        }
        if (donorsPaginationRequest.getPage() == null || donorsPaginationRequest.getPage() < 1) {
            donorsPaginationRequest.setPage(pageNo);
        }
    }

    public void activateDonors(UUID id, UUID userId) {
        Donors donors = donorsRepository.getByDonorId(id);
        if (donors == null) {
            throw new IllegalArgumentException("Donor Not found");
        }
        donors.setIsActive(Boolean.TRUE);
        donors.setUpdatedBy(userId);
        donors.setUpdatedOn(DateUtils.currentTimeIST());
        donorsRepository.update(donors);
    }

    public void deactivateDonors(UUID id, UUID userId) {

        Donors donors = donorsRepository.getByDonorId(id);
        if (donors == null) {
            throw new IllegalArgumentException("Donor Not found");
        }
        donors.setIsActive(Boolean.FALSE);
        donors.setUpdatedBy(userId);
        donors.setUpdatedOn(DateUtils.currentTimeIST());
        donorsRepository.update(donors);
    }

    public List<DonorDropdownResponse> getDonorDropdown()
    {
        return donorsRepository.getDonorDropdown();
    }

    public List<DonorDropdownResponse> getTenantDonorDropdown(UUID tenantOrgId)
    {
        return donorsRepository.getTenantDonorDropdown(tenantOrgId);
    }


    public void sendCommunicationToDonor(UUID donorId,
                                         CommunicationRequestDto communicationRequestDto)
            throws MessagingException {

        Donors donors = donorsRepository.getByDonorId(donorId);
        Organisation organisation = organizationRepository.getById(donors.getTenantOrgId());
        if (communicationRequestDto.getCommunicationModeEnums() == CommunicationModeEnums.EMAIL) {
            String templateName = communicationRequestDto.getTemplateIdOrName().trim();
            List<String> paramKeys = HelperClassToSendCommunication.EMAIL_TEMPLATE_PARAMS_MAP.get(templateName);
            if (paramKeys == null) {
                throw new IllegalArgumentException("No email parameter mapping found for template: " + templateName);
            }
            Map<String, Object> contextMap = helperClassToSendCommunication.buildEmailContextMap(donors, organisation);
            DonorEmailContextDTO donorEmailContextDTO = new DonorEmailContextDTO();
            donorEmailContextDTO.setDonorEmail(donors.getEmail());

            for (String key : paramKeys) {
                Object value = contextMap.getOrDefault(key, null);
                helperClassToSendCommunication.setDynamicEmailContextField(donorEmailContextDTO, key, value);
            }
            notificationManager.sendCommunicationMessage(donorEmailContextDTO, templateName + ".html");
        }

        else if (communicationRequestDto.getCommunicationModeEnums() == CommunicationModeEnums.WHATSAPP) {
            String templateName = communicationRequestDto.getTemplateIdOrName().trim();
//            String whatsappNumber = "+91" + donors.getMobileNumber();
            String whatsappNumber = "+916281569311";


            Map<String, String> paramMap = helperClassToSendCommunication.buildWatiParamMap(templateName, donors, organisation);

            List<WatiDTO.Parameters> parameters = new ArrayList<>();
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                parameters.add(WatiDTO.Parameters.builder()
                        .name(entry.getKey())
                        .value(entry.getValue())
                        .build());
            }
            WatiDTO watiDTO = WatiDTO.builder()
                    .template_name(templateName)
                    .broadcast_name(templateName)
                    .parameters(parameters)
                    .build();

            try {
                String jsonPayload = objectMapper.writeValueAsString(watiDTO);
                System.out.println("Sending WATI payload:\n" + jsonPayload);

                HttpResponse<String> response = watiService.sendTemplateMessage(whatsappNumber, watiDTO);

                if (response.statusCode() == 200) {
                    log.info("WhatsApp message sent successfully: {}, Body: {}", response.statusCode(), response.body());
                } else {
                    log.warn("Failed to send WhatsApp message. Status: {}, Body: {}", response.statusCode(), response.body());
                }
            } catch (IOException | InterruptedException e) {
                log.error("Exception while sending WhatsApp message", e);
                throw new RuntimeException("Failed to send template message via WATI", e);
            }
        }
    }
}
