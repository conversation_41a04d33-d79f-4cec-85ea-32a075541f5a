package com.chidhagni.donationreceipt.donorgroups;


import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonorGroupMappingDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonorGroupsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsPaginationRequest;
import com.chidhagni.donationreceipt.donorgroups.dto.response.DonorResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.donorgroups.utils.DonorGroupsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping.DONOR_GROUP_MAPPING;
import static com.chidhagni.donationreceipt.db.jooq.tables.DonorGroups.DONOR_GROUPS;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;

@Repository
@RequiredArgsConstructor
public class DonorGroupsRepository {

    private final DonorGroupsDao donorGroupsDao;
    private final DonorGroupMappingDao donorGroupMappingDao;
    private final IndividualRoleDao individualRoleDao;
    private final DonorsRepository donorsRepository;
    private final DonorGroupsMapper donorGroupMapper;

    @Value("${roles.superadmin}")
    private UUID superAdminRoleId;

    public DonorGroups create(DonorGroups donorGroups) {
        try{
            donorGroupsDao.insert(donorGroups);
        }
        catch (Exception ex){
            throw new RuntimeException("Exception while inserting the donor groups", ex);
        }
        return donorGroups;
    }

    public DonorGroups getById(UUID id) {
        DonorGroups donorGroups=null;
        try{
            donorGroups=donorGroupsDao.fetchOneById(id);
        }
        catch (Exception ex){
            throw new RuntimeException("Exception while fetching the donor groups by id", ex);
        }
        return donorGroups;
    }

    public void update(DonorGroups existingDonorGroups) {
        try {
            donorGroupsDao.update(existingDonorGroups);
        }
        catch (Exception ex){
            throw new RuntimeException("Exception while updating the donor groups", ex);
        }
    }

    public List<GetDonorGroupsResponse> getAllDonorGroupsResponses(Condition condition, List<SortField<?>> sortFields,
                                                                   DonorGroupsPaginationRequest paginationRequest,
                                                                   UserPrincipal userPrincipal) {
        try {
            Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
            Condition finalCondition = buildDonorAccessCondition(condition, userPrincipal);
            if (sortFields.isEmpty()) {
                sortFields.add(DONOR_GROUPS.CREATED_ON.desc());
            }
            List<GetDonorGroupsResponse> donorGroupsResponses = donorGroupsDao.ctx().select(
                            DONOR_GROUPS.ID,
                            DONOR_GROUPS.NAME,
                            DONOR_GROUPS.DESCRIPTION,
                            DONOR_GROUPS.FILTERS,
                            DONOR_GROUPS.CREATED_ON,
                            DONOR_GROUPS.UPDATED_ON,
                            DONOR_GROUPS.CREATED_BY,
                            DONOR_GROUPS.UPDATED_BY,
                            DONOR_GROUPS.IS_ACTIVE,
                            DONOR_GROUPS.ORG_ID
                    )
                    .from(DONOR_GROUPS)
                    .where(finalCondition)
                    .orderBy(sortFields)
                    .limit(paginationRequest.getPageSize())
                    .offset(pageNo)
                    .fetchInto(GetDonorGroupsResponse.class);

            for (GetDonorGroupsResponse response : donorGroupsResponses) {
                List<DonorGroupMapping> donorGroupMappings = getDonorGroupMappingsByGroupId(response.getId());
                List<DonorResponse> donorResponseList = new ArrayList<>();
                for (DonorGroupMapping donorGroupMapping : donorGroupMappings) {
                    if (!donorGroupMapping.getIsActive()) {
                        continue;
                    }
                    else {
                        Donors donor = donorsRepository.getByDonorId(donorGroupMapping.getDonorId());
                        if (donor != null) {
                            donorResponseList.add(donorGroupMapper.mapToDonorResponse(donor));
                        }
                    }
                }
                response.setDonorResponseList(donorResponseList);
            }

            return donorGroupsResponses;
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching all Donor Groups", ex);
        }
    }

    public void addDonorsToGroup(UUID groupId, List<UUID> donorIds, UserPrincipal userPrincipal) {
        DonorGroupMapping donorGroupMapping=new DonorGroupMapping();
        try {
            for (UUID donorId : donorIds) {

                UUID existingMappingId = donorGroupMappingDao.fetchOneByDonorIdAndGroupId(donorId, groupId);
                if(existingMappingId!=null) {
                    DonorGroupMapping existingMapping = donorGroupMappingDao.fetchOneById(existingMappingId);
                    existingMapping.setIsActive(Boolean.TRUE);
                    existingMapping.setUpdatedBy(userPrincipal.getId());
                    existingMapping.setUpdatedOn(DateUtils.currentTimeIST());
                    donorGroupMappingDao.update(existingMapping);
                }
                else {
                    donorGroupMapping.setId(UUID.randomUUID());
                    donorGroupMapping.setDonorId(donorId);
                    donorGroupMapping.setGroupId(groupId);
                    donorGroupMapping.setCreatedOn(DateUtils.currentTimeIST());
                    donorGroupMapping.setCreatedBy(userPrincipal.getId());
                    donorGroupMapping.setIsActive(Boolean.TRUE);
                    donorGroupMappingDao.insert(donorGroupMapping);
                }
            }
        }
        catch (Exception ex){
            throw new RuntimeException("Exception while adding donors to group", ex);
        }
    }

    public boolean existsByName(String name,UUID orgId) {

        return donorGroupsDao.ctx().fetchExists(
                donorGroupsDao.ctx().selectOne()
                        .from(DONOR_GROUPS).
                        where(DONOR_GROUPS.NAME.eq(name).and(DONOR_GROUPS.ORG_ID.eq(orgId)))
        );
    }

    public List<DonorGroupMapping> getDonorGroupMappingsByGroupId(UUID id) {
        return donorGroupMappingDao.ctx()
                .select()
                .from(DONOR_GROUP_MAPPING)
                .where(DONOR_GROUP_MAPPING.GROUP_ID.eq(id))
                .fetchInto(DonorGroupMapping.class);
    }

    private Condition buildDonorAccessCondition(Condition baseCondition, UserPrincipal userPrincipal) {
        UUID userId = userPrincipal.getId();

        boolean isSuperAdmin = individualRoleDao.ctx().fetchExists(
                individualRoleDao.ctx().selectOne()
                        .from(INDIVIDUAL_ROLE)
                        .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userId)
                                .and(INDIVIDUAL_ROLE.ROLE_ID.eq(superAdminRoleId)))
        );

        if (isSuperAdmin) {
            return baseCondition != null ? baseCondition : DSL.trueCondition();
        }

        List<UUID> userOrgIds = individualRoleDao.ctx().select(INDIVIDUAL_ROLE.ORG_ID)
                .from(INDIVIDUAL_ROLE)
                .where(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(userId))
                .fetchInto(UUID.class);

        if (userOrgIds.isEmpty()) {
            return DSL.falseCondition();
        }

        Condition orgCondition = DONOR_GROUPS.ORG_ID.in(userOrgIds);
        return baseCondition != null ? baseCondition.and(orgCondition) : orgCondition;
    }

    public List<UUID> getDonorGroupMappingsByGroupIdUUID(UUID id) {
        return donorGroupMappingDao.ctx()
                .select(DONOR_GROUP_MAPPING.DONOR_ID)
                .from(DONOR_GROUP_MAPPING)
                .where(DONOR_GROUP_MAPPING.GROUP_ID.eq(id))
                .fetchInto(UUID.class);
    }

    public void removeDonorsFromGroup(UUID id, ArrayList<UUID> uuids) {
        try {
            donorGroupMappingDao.ctx().update(DONOR_GROUP_MAPPING)
                    .set(DONOR_GROUP_MAPPING.IS_ACTIVE, Boolean.FALSE)
                    .where(DONOR_GROUP_MAPPING.GROUP_ID.eq(id)
                            .and(DONOR_GROUP_MAPPING.DONOR_ID.in(uuids)))
                    .execute();
        }
        catch (Exception ex){
            throw new RuntimeException("Exception while removing donors from group", ex);
        }
    }

    public DonorGroupMapping fetchOneByDonorIdAndGroupId(UUID donorId, UUID groupId) {
        return donorGroupMappingDao.ctx().select()
                .from(DONOR_GROUP_MAPPING)
                .where(DONOR_GROUP_MAPPING.DONOR_ID.eq(donorId)
                        .and(DONOR_GROUP_MAPPING.GROUP_ID.eq(groupId)))
                .fetchOneInto(DonorGroupMapping.class);
    }

    public List<UUID> getAllMappedDonorIdsByGroupId(UUID id) {
        return donorGroupMappingDao.ctx().select(DONOR_GROUP_MAPPING.DONOR_ID)
                .from(DONOR_GROUP_MAPPING)
                .where(DONOR_GROUP_MAPPING.GROUP_ID.eq(id))
                .fetchInto(UUID.class);
    }

    public List<UUID> getActiveMappedDonorIdsByGroupId(UUID id) {
        return donorGroupMappingDao.ctx().select(DONOR_GROUP_MAPPING.DONOR_ID)
                .from(DONOR_GROUP_MAPPING)
                .where(DONOR_GROUP_MAPPING.GROUP_ID.eq(id)
                        .and(DONOR_GROUP_MAPPING.IS_ACTIVE.eq(Boolean.TRUE)))
                .fetchInto(UUID.class);
    }
}
