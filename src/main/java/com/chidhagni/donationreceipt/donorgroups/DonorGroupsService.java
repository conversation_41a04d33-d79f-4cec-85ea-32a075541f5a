package com.chidhagni.donationreceipt.donorgroups;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.donorgroups.constants.CommunicationModeEnums;
import com.chidhagni.donationreceipt.donorgroups.dto.request.CommunicationRequestDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorEmailContextDTO;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsPaginationRequest;
import com.chidhagni.donationreceipt.donorgroups.dto.response.DonorResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetAllDonorGroupsResponses;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.donorgroups.utils.DonorGroupsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.wati.TemplateMessagesDTO;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.HelperClassToSendCommunication;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class DonorGroupsService {

    private final DonorGroupsRepository donorGroupsRepository;
    private final DonorGroupsMapper donorGroupMapper;
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;
    private final DonorsRepository donorsRepository;
    private final NotificationManager notificationManager;
    private final OrganizationRepository organizationRepository;
    private final WatiService watiService;
    private final ObjectMapper objectMapper;
    private final HelperClassToSendCommunication helperClassToSendCommunication;

    public UUID createDonorGroups(DonorGroupsDto donorGroupsDto, UserPrincipal userPrincipal) {

        boolean isAlreadyExistByName=donorGroupsRepository.existsByName(donorGroupsDto.getName(),donorGroupsDto.getOrgId());
        if(isAlreadyExistByName){
            throw new IllegalArgumentException("Donor Groups Already Exists with the given name.Please try with different name");
        }
        DonorGroups donorGroups = donorGroupMapper.mapToDonorGroups(donorGroupsDto, userPrincipal);
        DonorGroups createdDonorGroups = donorGroupsRepository.create(donorGroups);

        if (donorGroupsDto.getDonorIds() != null && !donorGroupsDto.getDonorIds().isEmpty()) {
            donorGroupsRepository.addDonorsToGroup(createdDonorGroups.getId(), donorGroupsDto.getDonorIds(),userPrincipal);
        }
        return createdDonorGroups.getId();
    }

    public GetDonorGroupsResponse getDonorGroupsById(UUID id) {
        DonorGroups donorGroups = donorGroupsRepository.getById(id);
        if (donorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }
        List<DonorResponse> donorResponseList=new ArrayList<>();
        List<DonorGroupMapping> donorGroupMappings = donorGroupsRepository.getDonorGroupMappingsByGroupId(id);
        for(DonorGroupMapping donorGroupMapping:donorGroupMappings)
        {
            DonorGroupMapping checkInActive=
                    donorGroupsRepository.fetchOneByDonorIdAndGroupId(donorGroupMapping.getDonorId(),donorGroupMapping.getGroupId());
            if(!checkInActive.getIsActive()){
                continue;
            }
            else{
                Donors donors = donorsRepository.getByDonorId(donorGroupMapping.getDonorId());
                donorResponseList.add(donorGroupMapper.mapToDonorResponse(donors));
            }
        }
        return donorGroupMapper.mapToDonorGroupsResponse(donorGroups,donorResponseList);
    }


    public GetDonorGroupsResponse updateDonorGroups(UUID id, DonorGroupsDto donorGroupsDto,
                                                    UserPrincipal userPrincipal) {
        DonorGroups existingDonorGroups = donorGroupsRepository.getById(id);
        if (existingDonorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }

        // Update fields
        existingDonorGroups.setName(donorGroupsDto.getName());
        existingDonorGroups.setDescription(donorGroupsDto.getDescription());
        existingDonorGroups.setFilters(donorGroupsDto.getFilters());
        existingDonorGroups.setUpdatedBy(userPrincipal.getId());
        existingDonorGroups.setUpdatedOn(DateUtils.currentTimeIST());
        existingDonorGroups.setOrgId(donorGroupsDto.getOrgId());

        donorGroupsRepository.update(existingDonorGroups);


        // Fetch both active and inactive mappings (full history)
        List<UUID> allMappedDonorIds = donorGroupsRepository.getAllMappedDonorIdsByGroupId(id); // includes both active and inactive
        List<UUID> activeMappedDonorIds = donorGroupsRepository.getActiveMappedDonorIdsByGroupId(id); // only active ones

        List<UUID> newDonorIds = donorGroupsDto.getDonorIds() != null ? donorGroupsDto.getDonorIds() : new ArrayList<>();

        Set<UUID> allSet = new HashSet<>(allMappedDonorIds); // full history
        Set<UUID> activeSet = new HashSet<>(activeMappedDonorIds); // currently active
        Set<UUID> newSet = new HashSet<>(newDonorIds);

       // Donors to add (new ones or reactivating old ones)
        Set<UUID> toAdd = new HashSet<>(newSet);
        toAdd.removeAll(activeSet);

        // Donors to remove (deactivate if currently active but no longer present in new)
        Set<UUID> toRemove = new HashSet<>(activeSet);
        toRemove.removeAll(newSet);


        if (!toAdd.isEmpty()) {
            donorGroupsRepository.addDonorsToGroup(existingDonorGroups.getId(), new ArrayList<>(toAdd), userPrincipal);
        }

        if (!toRemove.isEmpty()) {
            donorGroupsRepository.removeDonorsFromGroup(existingDonorGroups.getId(), new ArrayList<>(toRemove));
        }
        List<DonorGroupMapping> donorGroupMappings = donorGroupsRepository.getDonorGroupMappingsByGroupId(id);
        List<DonorResponse> donorResponseList = new ArrayList<>();
        for (DonorGroupMapping mapping : donorGroupMappings) {
            Donors donor = donorsRepository.getByDonorId(mapping.getDonorId());
            donorResponseList.add(donorGroupMapper.mapToDonorResponse(donor));
        }

        return donorGroupMapper.mapToDonorGroupsResponse(existingDonorGroups, donorResponseList);
    }



    public void activateDonorGroups(UUID id, UUID userId) {
        DonorGroups donorGroups = donorGroupsRepository.getById(id);
        if (donorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }
        donorGroups.setIsActive(Boolean.TRUE);
        donorGroups.setUpdatedBy(userId);
        donorGroups.setUpdatedOn(DateUtils.currentTimeIST());
        donorGroupsRepository.update(donorGroups);
    }

    public void deactivateDonorGroups(UUID id, UUID userId) {
        DonorGroups donorGroups = donorGroupsRepository.getById(id);
        if (donorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }
        donorGroups.setIsActive(Boolean.FALSE);
        donorGroups.setUpdatedBy(userId);
        donorGroups.setUpdatedOn(DateUtils.currentTimeIST());
        donorGroupsRepository.update(donorGroups);
    }


    public GetAllDonorGroupsResponses getAllDonorGroupsResponses(DonorGroupsPaginationRequest paginationRequest, UserPrincipal userPrincipal) {
        applyDefaults(paginationRequest);
        Condition finalCondition = null;
        List<SortField<?>> sortFields = new ArrayList<>();
        List<GetDonorGroupsResponse> allDonorGroupsResponses =
                donorGroupsRepository.getAllDonorGroupsResponses(finalCondition, sortFields, paginationRequest,userPrincipal);
        int count = allDonorGroupsResponses.size();
        return GetAllDonorGroupsResponses.builder()
                .donorGroupsResponses(allDonorGroupsResponses)
                .rowCount(count)
                .build();
    }

    public void applyDefaults(DonorGroupsPaginationRequest paginationRequest) {
        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
            paginationRequest.setPageSize(pageSize);
        }
        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
            paginationRequest.setPage(pageNo);
        }
    }


    public void sendCommunicationToDonorInDonorGroup(UUID donorGroupId, CommunicationRequestDto communicationRequestDto)
            throws MessagingException {
        List<UUID> donorId = donorGroupsRepository.getAllMappedDonorIdsByGroupId(donorGroupId);
        DonorGroups donorGroups = donorGroupsRepository.getById(donorGroupId);
        Organisation organisation = organizationRepository.getById(donorGroups.getOrgId());
        if (communicationRequestDto.getCommunicationModeEnums() == CommunicationModeEnums.EMAIL) {
            String templateName = communicationRequestDto.getTemplateIdOrName().trim();
            List<String> paramKeys = HelperClassToSendCommunication.EMAIL_TEMPLATE_PARAMS_MAP.get(templateName);

            if (paramKeys == null) {
                throw new IllegalArgumentException("No email parameter mapping found for template: " + templateName);
            }

            for (UUID donorIdItem : donorId) {
                Donors donor = donorsRepository.getByDonorId(donorIdItem);
                Map<String, Object> contextMap = helperClassToSendCommunication.buildEmailContextMap(donor, organisation);

                DonorEmailContextDTO donorEmailContextDTO = new DonorEmailContextDTO();
                donorEmailContextDTO.setDonorEmail(donor.getEmail());

                for (String key : paramKeys) {
                    Object value = contextMap.getOrDefault(key, null);
                    helperClassToSendCommunication.setDynamicEmailContextField(donorEmailContextDTO, key, value);
                }

                notificationManager.sendCommunicationMessage(donorEmailContextDTO, templateName);
            }
        }
        else if (communicationRequestDto.getCommunicationModeEnums() == CommunicationModeEnums.WHATSAPP) {
            String templateName = communicationRequestDto.getTemplateIdOrName().trim();
            log.info("Attempting to send WhatsApp template: {}", templateName);

            // Validate template name
            if (templateName == null || templateName.isEmpty()) {
                throw new IllegalArgumentException("Template name cannot be null or empty");
            }

            List<TemplateMessagesDTO.Receivers> receivers = new ArrayList<>();

            for (UUID donorIdItem : donorId) {
                Donors donor = donorsRepository.getByDonorId(donorIdItem);

                // Validate donor has mobile number
                if (donor.getMobileNumber() == null || donor.getMobileNumber().trim().isEmpty()) {
                    log.warn("Skipping donor {} - no mobile number", donor.getName());
                    continue;
                }

                TemplateMessagesDTO.Receivers receiver = new TemplateMessagesDTO.Receivers();
                receiver.setWhatsappNumber("+91" + donor.getMobileNumber().trim());

                try {
                    Map<String, String> paramMap = helperClassToSendCommunication.buildWatiParamMap(templateName, donor, organisation);
                    List<TemplateMessagesDTO.CustomParams> customParams = new ArrayList<>();

                    for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                        // Validate parameter values are not null or empty
                        String value = entry.getValue();
                        if (value == null || value.trim().isEmpty()) {
                            log.warn("Parameter {} has null/empty value for donor {}", entry.getKey(), donor.getName());
                            value = "N/A"; // Provide default value
                        }
                        customParams.add(createCustomParam(entry.getKey(), value.trim()));
                    }
                    receiver.setCustomParams(customParams);
                    receivers.add(receiver);
                } catch (IllegalArgumentException e) {
                    log.error("Unsupported template '{}' for donor {}: {}", templateName, donor.getName(), e.getMessage());
                    throw e;
                }
            }

            if (receivers.isEmpty()) {
                throw new IllegalArgumentException("No valid recipients found for WhatsApp communication");
            }

            TemplateMessagesDTO templateMessagesDTO = new TemplateMessagesDTO();
            templateMessagesDTO.setTemplate_name(templateName);
            templateMessagesDTO.setBroadcast_name(templateName + "_broadcast");
            templateMessagesDTO.setReceivers(receivers);

            templateMessagesDTO.setReceivers(Collections.singletonList(receiver));

            try {
                String jsonPayload = objectMapper.writeValueAsString(templateMessagesDTO);
                System.out.println("Sending to WATI:\n" + jsonPayload);

                HttpResponse<String> response = watiService.sendTemplateMessages(templateMessagesDTO);
                if (response.statusCode() == 200) {
                    log.info("Whatsapp messages sent successfully: {}, Body: {}",
                            response.statusCode(), response.body());
                } else {
                    log.warn("WATI response returned with status {}: {}", response.statusCode(), response.body());
                }
            } catch (IOException | InterruptedException e) {
                log.error("Failed to send template messages", e);
                throw new RuntimeException("Failed to send template messages via WATI", e);
            }
        }
        else if (communicationRequestDto.getCommunicationModeEnums() == CommunicationModeEnums.SMS) {
            log.info("SMS communication mode selected");
        } else {
            throw new IllegalArgumentException("Invalid communication mode: " +
                    communicationRequestDto.getCommunicationModeEnums());
        }
    }

    private TemplateMessagesDTO.CustomParams createCustomParam(String name, String value) {
        TemplateMessagesDTO.CustomParams param = new TemplateMessagesDTO.CustomParams();
        param.setName(name);
        param.setValue(value);
        return param;
    }
}
